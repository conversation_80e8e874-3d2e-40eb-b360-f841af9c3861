# Epidemiology Intelligence Agent 

## Project Purpose
This is a **therapy- and indication-agnostic epidemiology agent** designed to fetch, synthesize, and structure epidemiology data across US & Ex-US geographies. The agent focuses on providing ready-to-use epidemiological insights without being tied to specific therapeutic areas or medical indications.

## Core Capabilities
- **Configure Epidemiology Research**: User should be able to configure details such as 
    1. Core epidemiology outcomes (incidence rate, prevalence rate, diagnosed patients, treated patients, and forecasted patients)
    2. Select Geographies (multi-select options: Continents)
    3. Select Demographic Stratification: (multi-select options: age, sex, ethnicity)
- **PubMed Data Extraction**: 
    - Create a different combinations of pubmed queries based on user selection.
    - Iterate through each query for achieving following tasks
        - Fetch 10 pubmed results for given query
        - Determine which results are relevant based on title and abstract to given query. 
        - Filter the results for which Fulltext URLs are available.
    - Use Website Reader to read those URLs and fetch them as documents.
    - Use Agentic Chunker to split the documents and ingest them into knowledge base (pgvector DB).
- **Q&A Functionality for Detailed Insight Synthesis**: Provide chat functionality to answer user queries about ingested epidemiological data with citations.
    - **Geographic Analysis**: Compare data across countries and regions to given summary of geographical analysis.
    - **Demographic Analysis**: Summarize data by ethnicity, age, and sex (if available)
    - **Cross-Country Comparisons**: Generate comparative analyses between different geographic regions

## Technical Details:
- Use OPENAI provider modele everywhere.
- Use pgvector for vector database.